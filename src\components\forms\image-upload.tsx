'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';

interface ImageUploadProps {
  onUpload: (imageData: {
    url: string;
    public_id: string;
    width?: number;
    height?: number;
    format?: string;
    size?: number;
    compression_ratio?: string;
    optimized?: boolean;
    preset?: string;
  }) => void;
  onError?: (error: string) => void;
  currentImage?: string;
  folder?: string;
  className?: string;
  onPreview?: () => void;
  preset?: 'blog-post' | 'featured-image' | 'thumbnail' | 'avatar' | 'custom';
  optimize?: boolean;
  customSize?: { width: number; height: number };
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  onUpload,
  onError,
  currentImage,
  folder = 'wikify-blog',
  className = '',
  onPreview,
  preset = 'featured-image',
  optimize = true,
  customSize,
}) => {
  const [uploading, setUploading] = useState(false);
  const [preview, setPreview] = useState<string | null>(currentImage || null);
  const [imageLoading, setImageLoading] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [optimizationInfo, setOptimizationInfo] = useState<{
    format?: string;
    compression_ratio?: string;
    optimized?: boolean;
    preset?: string;
  } | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Update preview when currentImage prop changes
  useEffect(() => {
    console.log('🔄 ImageUpload: currentImage changed:', currentImage ? currentImage.substring(0, 50) + '...' : 'null');
    setPreview(currentImage || null);
    setImageError(false);
    setImageLoading(false); // Start with loading false, let the img onLoad handle it
  }, [currentImage]);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    console.log('File selected:', {
      name: file.name,
      type: file.type,
      size: file.size,
      lastModified: file.lastModified
    });

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      onError?.('Please select a valid image file (JPEG, PNG, GIF, or WebP)');
      return;
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      onError?.('File size must be less than 10MB');
      return;
    }

    // Show preview with error handling
    if (typeof FileReader !== 'undefined') {
      try {
        const reader = new FileReader();
        reader.onload = (e) => {
          if (e.target?.result) {
            setPreview(e.target.result as string);
            setImageError(false);
            setImageLoading(false);
          }
        };
        reader.onerror = () => {
          console.error('FileReader error');
          onError?.('Failed to read file');
        };
        reader.readAsDataURL(file);
      } catch (error) {
        console.error('FileReader creation error:', error);
        onError?.('Failed to process file');
      }
    } else {
      console.warn('FileReader not available');
      // Fallback: just show a placeholder or skip preview
      setPreview('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIFNlbGVjdGVkPC90ZXh0Pjwvc3ZnPg==');
    }

    // Upload file
    uploadFile(file);
  };

  const uploadFile = async (file: File) => {
    setUploading(true);

    try {
      console.log('Uploading file with optimization:', {
        name: file.name,
        type: file.type,
        size: file.size,
        folder: folder,
        preset: preset,
        optimize: optimize,
        customSize: customSize
      });

      // Create FormData safely
      const formData = new FormData();
      formData.append('file', file);
      formData.append('folder', folder);
      formData.append('preset', preset);
      formData.append('optimize', optimize.toString());

      if (customSize) {
        formData.append('width', customSize.width.toString());
        formData.append('height', customSize.height.toString());
      }

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      console.log('Upload response status:', response.status);
      const result = await response.json();
      console.log('Upload result:', result);

      if (result.success && result.data?.url) {
        // Update preview with uploaded URL
        setPreview(result.data.url);

        // Store optimization info
        setOptimizationInfo({
          format: result.data.format,
          compression_ratio: result.data.compression_ratio,
          optimized: result.data.optimized,
          preset: result.data.preset,
        });

        // Call onUpload callback with optimization details
        onUpload({
          url: result.data.url,
          public_id: result.data.public_id,
          width: result.data.width,
          height: result.data.height,
          format: result.data.format,
          size: result.data.size,
          compression_ratio: result.data.compression_ratio,
          optimized: result.data.optimized,
          preset: result.data.preset,
        });

        console.log('Image uploaded successfully:', result.data.url);
      } else {
        onError?.(result.error || result.details || 'Upload failed');
        // Keep the local preview if upload fails
      }
    } catch (error) {
      console.error('Upload error:', error);
      onError?.(error instanceof Error ? error.message : 'Upload failed. Please try again.');
      // Keep the local preview if upload fails
    } finally {
      setUploading(false);
    }
  };

  const handleRemove = () => {
    setPreview(null);
    setImageError(false);
    setImageLoading(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  console.log('ImageUpload render:', {
    preview: preview ? preview.substring(0, 50) + '...' : null,
    imageLoading,
    imageError,
    currentImage: currentImage ? currentImage.substring(0, 50) + '...' : null
  });

  return (
    <div className={`space-y-4 ${className}`}>
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />

      {preview ? (
        /* Enhanced Preview */
        <div className="relative group">
          <div
            className="relative overflow-hidden rounded-xl shadow-lg cursor-pointer transition-all duration-300"
            style={{
              backgroundColor: 'white',
              border: '2px solid #e5e7eb',
              minHeight: '200px'
            }}
            onClick={onPreview}
            title="Click to preview full size"
          >
            {/* Simple Image Preview Container */}
            <div
              style={{
                backgroundColor: '#f9fafb',
                borderRadius: '8px',
                padding: '12px',
                minHeight: '200px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                position: 'relative'
              }}
            >
              {/* Loading Overlay */}
              {imageLoading && (
                <div
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(255,255,255,0.9)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderRadius: '8px',
                    zIndex: 10
                  }}
                >
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              )}

              {/* Error State */}
              {imageError && !imageLoading ? (
                <div className="text-center text-red-500 p-4">
                  <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                  <p className="text-sm mb-2">Failed to load image</p>
                  <button
                    onClick={() => {
                      setImageError(false);
                      setImageLoading(false);
                    }}
                    className="px-3 py-1 bg-red-100 text-red-700 rounded text-sm hover:bg-red-200"
                  >
                    Retry
                  </button>
                </div>
              ) : (
                /* Image Display */
                <img
                  src={preview}
                  alt="Featured image preview"
                  style={{
                    display: 'block',
                    maxWidth: '100%',
                    width: 'auto',
                    height: 'auto',
                    maxHeight: '320px',
                    objectFit: 'contain',
                    backgroundColor: 'white',
                    borderRadius: '4px',
                    boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                  }}
                  onLoad={(e) => {
                    console.log('✅ Preview image loaded successfully:', preview.substring(0, 50));
                    setImageLoading(false);
                    setImageError(false);
                  }}
                  onError={(e) => {
                    console.error('❌ Preview image failed to load:', preview.substring(0, 50));
                    setImageLoading(false);
                    setImageError(true);
                  }}
                />
              )}
            </div>

            {/* Overlay with action buttons */}
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
              <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex gap-2">
                {onPreview && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={onPreview}
                    disabled={uploading}
                    className="bg-white/90 backdrop-blur-sm border-white/50 text-gray-700 hover:bg-white"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    Preview
                  </Button>
                )}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={uploading}
                  className="bg-white/90 backdrop-blur-sm border-white/50 text-gray-700 hover:bg-white"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                  </svg>
                  Change
                </Button>
                <Button
                  type="button"
                  variant="danger"
                  size="sm"
                  onClick={handleRemove}
                  disabled={uploading}
                  className="bg-red-500/90 backdrop-blur-sm border-red-500/50 text-white hover:bg-red-600"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  Remove
                </Button>
              </div>
            </div>
          </div>

          {/* Image Info */}
          <div className="mt-3 p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between text-sm text-gray-600">
              <span className="flex items-center">
                <svg className="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                {preview.startsWith('data:') ? 'Image selected' : 'Image uploaded successfully'}
              </span>
              {!preview.startsWith('data:') && (
                <a
                  href={preview}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 text-xs underline"
                >
                  View full size
                </a>
              )}
            </div>



          </div>
        </div>
      ) : (
        /* Enhanced Upload Area */
        <div
          onClick={() => fileInputRef.current?.click()}
          className="group border-2 border-dashed border-gray-300 rounded-xl p-8 text-center cursor-pointer hover:border-blue-400 hover:bg-blue-50/50 transition-all duration-300"
        >
          <div className="space-y-4">
            <div className="text-gray-400 group-hover:text-blue-500 transition-colors">
              <svg className="mx-auto h-16 w-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <div>
              <p className="text-lg font-medium text-gray-700 group-hover:text-blue-600 transition-colors">
                Click to upload an image
              </p>
              <p className="text-sm text-gray-500 mt-2">
                Drag and drop or click to browse
              </p>
              <p className="text-xs text-gray-400 mt-1">
                PNG, JPG, GIF, WebP up to 10MB
              </p>

            </div>
          </div>
        </div>
      )}

      {uploading && (
        <div className="space-y-3">
          <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full animate-pulse transition-all duration-300" style={{ width: '60%' }}></div>
          </div>
          <div className="flex items-center justify-center">
            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <p className="text-sm text-gray-600">Uploading your image...</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageUpload;
