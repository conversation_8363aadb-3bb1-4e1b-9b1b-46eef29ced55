'use client';

import { useState } from 'react';

export default function DebugImage() {
  const [imageUrl, setImageUrl] = useState<string>('');
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState(false);

  const testUrls = [
    'https://picsum.photos/800/400',
    'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=400&fit=crop',
    'https://res.cloudinary.com/demo/image/upload/sample.jpg',
    'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjNGY0NmU1Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5EZWJ1ZyBJbWFnZSA4MDB4NDAwPC90ZXh0Pjwvc3ZnPg=='
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-center mb-8">Debug Image Component</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Test URLs */}
          <div className="bg-white p-6 rounded-lg shadow-lg">
            <h2 className="text-xl font-semibold mb-4">Test URLs</h2>
            <div className="space-y-2">
              {testUrls.map((url, index) => (
                <button
                  key={index}
                  onClick={() => {
                    setImageUrl(url);
                    setError(false);
                  }}
                  className="block w-full text-left px-3 py-2 bg-blue-50 hover:bg-blue-100 rounded text-sm"
                >
                  Test {index + 1}: {url.substring(0, 50)}...
                </button>
              ))}
              <button
                onClick={() => {
                  setImageUrl('');
                  setError(false);
                }}
                className="block w-full text-left px-3 py-2 bg-gray-50 hover:bg-gray-100 rounded text-sm"
              >
                Clear
              </button>
            </div>
          </div>

          {/* Image Preview */}
          <div className="bg-white p-6 rounded-lg shadow-lg">
            <h2 className="text-xl font-semibold mb-4">Image Preview</h2>
            
            {/* Exact same structure as ImageUpload component */}
            <div 
              style={{
                backgroundColor: '#f8fafc',
                border: '2px solid #e2e8f0',
                borderRadius: '12px',
                padding: '16px',
                minHeight: '220px',
                position: 'relative',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              {/* Show loading spinner when uploading */}
              {uploading && (
                <div style={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  zIndex: 20
                }}>
                  <div style={{
                    width: '40px',
                    height: '40px',
                    border: '4px solid #e5e7eb',
                    borderTop: '4px solid #3b82f6',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite'
                  }}></div>
                </div>
              )}

              {/* Show error message */}
              {error && !uploading && imageUrl && (
                <div style={{ textAlign: 'center', color: '#ef4444' }}>
                  <svg style={{ width: '48px', height: '48px', margin: '0 auto 8px' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                  <p style={{ fontSize: '14px', margin: '8px 0' }}>Failed to load image</p>
                  <button
                    onClick={() => setError(false)}
                    style={{
                      padding: '6px 12px',
                      backgroundColor: '#fee2e2',
                      color: '#dc2626',
                      border: 'none',
                      borderRadius: '6px',
                      fontSize: '12px',
                      cursor: 'pointer'
                    }}
                  >
                    Retry
                  </button>
                </div>
              )}

              {/* Show image when not in error state */}
              {!error && !uploading && imageUrl ? (
                <img
                  src={imageUrl}
                  alt="Debug preview"
                  style={{ 
                    display: 'block',
                    maxWidth: '100%',
                    width: 'auto',
                    height: 'auto',
                    maxHeight: '300px',
                    objectFit: 'contain',
                    borderRadius: '8px',
                    backgroundColor: 'white',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                  }}
                  onLoad={() => {
                    console.log('✅ Debug image loaded successfully!');
                    setError(false);
                  }}
                  onError={() => {
                    console.error('❌ Debug image failed to load!');
                    setError(true);
                  }}
                />
              ) : !imageUrl ? (
                <div style={{ textAlign: 'center', color: '#6b7280' }}>
                  <svg style={{ width: '48px', height: '48px', margin: '0 auto 8px' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <p>Select a test image</p>
                </div>
              ) : null}
            </div>

            {/* Debug Info */}
            <div className="mt-4 p-3 bg-gray-100 rounded text-sm">
              <h4 className="font-medium mb-2">Debug Info:</h4>
              <p><strong>Image URL:</strong> {imageUrl || 'None'}</p>
              <p><strong>Error:</strong> {error ? 'Yes' : 'No'}</p>
              <p><strong>Uploading:</strong> {uploading ? 'Yes' : 'No'}</p>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}
