'use client';

import { useState } from 'react';
import ImageUpload from '@/components/forms/image-upload';

export default function TestImageUpload() {
  const [imageUrl, setImageUrl] = useState<string>('');
  const [error, setError] = useState<string>('');

  const handleImageUpload = (imageData: {
    url: string;
    public_id: string;
    width?: number;
    height?: number;
    format?: string;
    size?: number;
    compression_ratio?: string;
    optimized?: boolean;
    preset?: string;
  }) => {
    console.log('Image uploaded:', imageData);
    setImageUrl(imageData.url);
  };

  const handleError = (errorMessage: string) => {
    console.error('Upload error:', errorMessage);
    setError(errorMessage);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-2xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-center mb-8">Image Upload Test</h1>
        
        {error && (
          <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        <div className="bg-white p-6 rounded-lg shadow-lg">
          <h2 className="text-xl font-semibold mb-4">Upload Image</h2>
          
          <ImageUpload
            currentImage={imageUrl}
            onUpload={handleImageUpload}
            onError={handleError}
            folder="test"
            preset="featured-image"
            optimize={true}
            onPreview={() => console.log('Preview clicked')}
          />

          {imageUrl && (
            <div className="mt-6">
              <h3 className="text-lg font-medium mb-2">Current Image URL:</h3>
              <p className="text-sm text-gray-600 break-all bg-gray-100 p-2 rounded">
                {imageUrl}
              </p>
            </div>
          )}

          <div className="mt-6">
            <h3 className="text-lg font-medium mb-2">Test with Static Image:</h3>
            <div className="space-x-2 mb-4">
              <button
                onClick={() => setImageUrl('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=400&fit=crop')}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Load Unsplash Image
              </button>
              <button
                onClick={() => setImageUrl('https://picsum.photos/800/400')}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
              >
                Load Picsum Image
              </button>
              <button
                onClick={() => setImageUrl('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjNGY0NmU1Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5UZXN0IEltYWdlIDgwMHg0MDA8L3RleHQ+PC9zdmc+')}
                className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
              >
                Load SVG Image
              </button>
              <button
                onClick={() => setImageUrl('')}
                className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
              >
                Clear Image
              </button>
            </div>

            <div className="mt-4 p-3 bg-gray-100 rounded text-sm">
              <h4 className="font-medium mb-2">Debug Info:</h4>
              <p><strong>Current Image URL:</strong> {imageUrl || 'None'}</p>
              <p><strong>Error:</strong> {error || 'None'}</p>
            </div>

            {/* Direct Image Test */}
            {imageUrl && (
              <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded">
                <h4 className="font-medium mb-2">Direct Image Test (for comparison):</h4>
                <img
                  src={imageUrl}
                  alt="Direct test"
                  style={{
                    maxWidth: '100%',
                    height: 'auto',
                    maxHeight: '300px',
                    objectFit: 'contain',
                    backgroundColor: 'white',
                    border: '1px solid #ccc',
                    borderRadius: '4px'
                  }}
                  onLoad={() => console.log('Direct image loaded')}
                  onError={() => console.log('Direct image failed')}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
