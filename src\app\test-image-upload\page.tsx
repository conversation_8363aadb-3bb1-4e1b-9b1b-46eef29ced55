'use client';

import { useState } from 'react';
import ImageUpload from '@/components/forms/image-upload';

export default function TestImageUpload() {
  const [imageUrl, setImageUrl] = useState<string>('');
  const [error, setError] = useState<string>('');

  const handleImageUpload = (imageData: {
    url: string;
    public_id: string;
    width?: number;
    height?: number;
    format?: string;
    size?: number;
    compression_ratio?: string;
    optimized?: boolean;
    preset?: string;
  }) => {
    console.log('Image uploaded:', imageData);
    setImageUrl(imageData.url);
  };

  const handleError = (errorMessage: string) => {
    console.error('Upload error:', errorMessage);
    setError(errorMessage);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-2xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-center mb-8">Image Upload Test</h1>
        
        {error && (
          <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        <div className="bg-white p-6 rounded-lg shadow-lg">
          <h2 className="text-xl font-semibold mb-4">Upload Image</h2>
          
          <ImageUpload
            currentImage={imageUrl}
            onUpload={handleImageUpload}
            onError={handleError}
            folder="test"
            preset="featured-image"
            optimize={true}
            onPreview={() => console.log('Preview clicked')}
          />

          {imageUrl && (
            <div className="mt-6">
              <h3 className="text-lg font-medium mb-2">Current Image URL:</h3>
              <p className="text-sm text-gray-600 break-all bg-gray-100 p-2 rounded">
                {imageUrl}
              </p>
            </div>
          )}

          <div className="mt-6">
            <h3 className="text-lg font-medium mb-2">Test with Static Image:</h3>
            <button
              onClick={() => setImageUrl('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=400&fit=crop')}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Load Test Image
            </button>
            <button
              onClick={() => setImageUrl('')}
              className="ml-2 px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              Clear Image
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
