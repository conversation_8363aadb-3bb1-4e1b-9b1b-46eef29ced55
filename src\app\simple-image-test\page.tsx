'use client';

import { useState } from 'react';

export default function SimpleImageTest() {
  const [imageUrl, setImageUrl] = useState<string>('');

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-2xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-center mb-8">Simple Image Test</h1>
        
        <div className="bg-white p-6 rounded-lg shadow-lg">
          <div className="mb-4">
            <button
              onClick={() => setImageUrl('https://picsum.photos/800/400')}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 mr-2"
            >
              Load Test Image
            </button>
            <button
              onClick={() => setImageUrl('')}
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              Clear
            </button>
          </div>

          {/* Simple Preview Area */}
          <div 
            className="border-2 border-dashed border-gray-300 rounded-lg p-4 min-h-[200px] flex items-center justify-center"
            style={{ backgroundColor: '#f9fafb' }}
          >
            {imageUrl ? (
              <div className="w-full">
                <img
                  src={imageUrl}
                  alt="Test preview"
                  className="w-full h-auto max-h-80 object-contain"
                  style={{
                    display: 'block',
                    maxWidth: '100%',
                    height: 'auto',
                    objectFit: 'contain',
                    backgroundColor: 'white',
                    borderRadius: '4px'
                  }}
                  onLoad={() => console.log('Image loaded successfully')}
                  onError={() => console.log('Image failed to load')}
                />
              </div>
            ) : (
              <div className="text-center text-gray-500">
                <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <p>No image selected</p>
              </div>
            )}
          </div>

          {imageUrl && (
            <div className="mt-4 p-3 bg-gray-100 rounded text-sm">
              <p><strong>Image URL:</strong> {imageUrl}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
